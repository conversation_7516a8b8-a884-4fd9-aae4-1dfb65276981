using System;
using System.Printing;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using DateFactoryApp.Models;

namespace DateFactoryApp.Helpers
{
    public class PrintService
    {
        public static void PrintInvoice(Sale sale)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    var document = CreateInvoiceDocument(sale);
                    printDialog.PrintDocument(document.DocumentPaginator, $"فاتورة رقم {sale.InvoiceNumber}");
                    
                    NotificationService.Success("تمت الطباعة بنجاح", $"تم طباعة الفاتورة رقم {sale.InvoiceNumber}");
                }
            }
            catch (Exception ex)
            {
                NotificationService.Error("خطأ في الطباعة", ex.Message);
            }
        }

        public static void PrintReceipt(Sale sale)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    var document = CreateReceiptDocument(sale);
                    printDialog.PrintDocument(document.DocumentPaginator, $"إيصال رقم {sale.InvoiceNumber}");
                    
                    NotificationService.Success("تمت الطباعة بنجاح", $"تم طباعة الإيصال رقم {sale.InvoiceNumber}");
                }
            }
            catch (Exception ex)
            {
                NotificationService.Error("خطأ في الطباعة", ex.Message);
            }
        }

        public static void PrintReport(string title, FrameworkElement content)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    var document = CreateReportDocument(title, content);
                    printDialog.PrintDocument(document.DocumentPaginator, title);
                    
                    NotificationService.Success("تمت الطباعة بنجاح", $"تم طباعة {title}");
                }
            }
            catch (Exception ex)
            {
                NotificationService.Error("خطأ في الطباعة", ex.Message);
            }
        }

        private static FlowDocument CreateInvoiceDocument(Sale sale)
        {
            var document = new FlowDocument
            {
                PageWidth = 793.7, // A4 width
                PageHeight = 1122.5, // A4 height
                PagePadding = new Thickness(50),
                FontFamily = new FontFamily("Arial"),
                FontSize = 12,
                FlowDirection = FlowDirection.RightToLeft
            };

            // Header
            var headerTable = new Table();
            headerTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });
            headerTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });

            var headerRowGroup = new TableRowGroup();
            var headerRow = new TableRow();

            // Company info
            var companyCell = new TableCell(new Paragraph(new Run("شركة التمور المتقدمة"))
            {
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Right
            });
            companyCell.Blocks.Add(new Paragraph(new Run("نظام إدارة مصنع ومحل التمور")));
            companyCell.Blocks.Add(new Paragraph(new Run("الرياض - المملكة العربية السعودية")));
            companyCell.Blocks.Add(new Paragraph(new Run("هاتف: +966 50 123 4567")));

            // Invoice info
            var invoiceCell = new TableCell(new Paragraph(new Run("فاتورة مبيعات"))
            {
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Left
            });
            invoiceCell.Blocks.Add(new Paragraph(new Run($"رقم الفاتورة: {sale.InvoiceNumber}")));
            invoiceCell.Blocks.Add(new Paragraph(new Run($"التاريخ: {sale.SaleDate:yyyy/MM/dd}")));
            invoiceCell.Blocks.Add(new Paragraph(new Run($"الوقت: {sale.SaleDate:HH:mm}")));

            headerRow.Cells.Add(companyCell);
            headerRow.Cells.Add(invoiceCell);
            headerRowGroup.Rows.Add(headerRow);
            headerTable.RowGroups.Add(headerRowGroup);

            document.Blocks.Add(headerTable);
            document.Blocks.Add(new Paragraph(new Run(" ")) { FontSize = 6 }); // Spacer

            // Customer info
            if (sale.Customer != null)
            {
                var customerParagraph = new Paragraph();
                customerParagraph.Inlines.Add(new Run("العميل: ") { FontWeight = FontWeights.Bold });
                customerParagraph.Inlines.Add(new Run(sale.Customer.Name));
                if (!string.IsNullOrEmpty(sale.Customer.Phone))
                {
                    customerParagraph.Inlines.Add(new Run($" - هاتف: {sale.Customer.Phone}"));
                }
                document.Blocks.Add(customerParagraph);
                document.Blocks.Add(new Paragraph(new Run(" ")) { FontSize = 6 }); // Spacer
            }

            // Items table
            var itemsTable = new Table
            {
                BorderBrush = Brushes.Black,
                BorderThickness = new Thickness(1)
            };

            itemsTable.Columns.Add(new TableColumn { Width = new GridLength(40) }); // #
            itemsTable.Columns.Add(new TableColumn { Width = new GridLength(200) }); // Product
            itemsTable.Columns.Add(new TableColumn { Width = new GridLength(60) }); // Qty
            itemsTable.Columns.Add(new TableColumn { Width = new GridLength(80) }); // Price
            itemsTable.Columns.Add(new TableColumn { Width = new GridLength(80) }); // Total

            // Table header
            var tableHeaderGroup = new TableRowGroup();
            var tableHeaderRow = new TableRow { Background = Brushes.LightGray };
            
            tableHeaderRow.Cells.Add(new TableCell(new Paragraph(new Run("#")) { TextAlignment = TextAlignment.Center }));
            tableHeaderRow.Cells.Add(new TableCell(new Paragraph(new Run("المنتج")) { TextAlignment = TextAlignment.Center }));
            tableHeaderRow.Cells.Add(new TableCell(new Paragraph(new Run("الكمية")) { TextAlignment = TextAlignment.Center }));
            tableHeaderRow.Cells.Add(new TableCell(new Paragraph(new Run("السعر")) { TextAlignment = TextAlignment.Center }));
            tableHeaderRow.Cells.Add(new TableCell(new Paragraph(new Run("الإجمالي")) { TextAlignment = TextAlignment.Center }));

            foreach (var cell in tableHeaderRow.Cells)
            {
                cell.BorderBrush = Brushes.Black;
                cell.BorderThickness = new Thickness(1);
                cell.Padding = new Thickness(5);
            }

            tableHeaderGroup.Rows.Add(tableHeaderRow);
            itemsTable.RowGroups.Add(tableHeaderGroup);

            // Table rows
            var tableRowGroup = new TableRowGroup();
            int itemNumber = 1;

            foreach (var item in sale.SaleItems)
            {
                var row = new TableRow();
                
                row.Cells.Add(new TableCell(new Paragraph(new Run(itemNumber.ToString())) { TextAlignment = TextAlignment.Center }));
                row.Cells.Add(new TableCell(new Paragraph(new Run(item.Product?.Name ?? "")) { TextAlignment = TextAlignment.Right }));
                row.Cells.Add(new TableCell(new Paragraph(new Run(item.Quantity.ToString("N0"))) { TextAlignment = TextAlignment.Center }));
                row.Cells.Add(new TableCell(new Paragraph(new Run(item.UnitPrice.ToString("C"))) { TextAlignment = TextAlignment.Center }));
                row.Cells.Add(new TableCell(new Paragraph(new Run(item.LineTotal.ToString("C"))) { TextAlignment = TextAlignment.Center }));

                foreach (var cell in row.Cells)
                {
                    cell.BorderBrush = Brushes.Black;
                    cell.BorderThickness = new Thickness(1);
                    cell.Padding = new Thickness(5);
                }

                tableRowGroup.Rows.Add(row);
                itemNumber++;
            }

            itemsTable.RowGroups.Add(tableRowGroup);
            document.Blocks.Add(itemsTable);

            // Totals
            document.Blocks.Add(new Paragraph(new Run(" ")) { FontSize = 10 }); // Spacer

            var totalsTable = new Table();
            totalsTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });
            totalsTable.Columns.Add(new TableColumn { Width = new GridLength(150) });

            var totalsRowGroup = new TableRowGroup();

            // Subtotal
            var subtotalRow = new TableRow();
            subtotalRow.Cells.Add(new TableCell(new Paragraph(new Run("المجموع الفرعي:")) { TextAlignment = TextAlignment.Right, FontWeight = FontWeights.Bold }));
            subtotalRow.Cells.Add(new TableCell(new Paragraph(new Run(sale.SubTotal.ToString("C"))) { TextAlignment = TextAlignment.Center }));
            totalsRowGroup.Rows.Add(subtotalRow);

            // Discount
            if (sale.DiscountAmount > 0)
            {
                var discountRow = new TableRow();
                discountRow.Cells.Add(new TableCell(new Paragraph(new Run("الخصم:")) { TextAlignment = TextAlignment.Right, FontWeight = FontWeights.Bold }));
                discountRow.Cells.Add(new TableCell(new Paragraph(new Run($"-{sale.DiscountAmount:C}")) { TextAlignment = TextAlignment.Center }));
                totalsRowGroup.Rows.Add(discountRow);
            }

            // Tax
            if (sale.TaxAmount > 0)
            {
                var taxRow = new TableRow();
                taxRow.Cells.Add(new TableCell(new Paragraph(new Run("الضريبة:")) { TextAlignment = TextAlignment.Right, FontWeight = FontWeights.Bold }));
                taxRow.Cells.Add(new TableCell(new Paragraph(new Run(sale.TaxAmount.ToString("C"))) { TextAlignment = TextAlignment.Center }));
                totalsRowGroup.Rows.Add(taxRow);
            }

            // Total
            var totalRow = new TableRow { Background = Brushes.LightGray };
            totalRow.Cells.Add(new TableCell(new Paragraph(new Run("الإجمالي:")) { TextAlignment = TextAlignment.Right, FontWeight = FontWeights.Bold, FontSize = 14 }));
            totalRow.Cells.Add(new TableCell(new Paragraph(new Run(sale.TotalAmount.ToString("C"))) { TextAlignment = TextAlignment.Center, FontWeight = FontWeights.Bold, FontSize = 14 }));
            totalsRowGroup.Rows.Add(totalRow);

            totalsTable.RowGroups.Add(totalsRowGroup);
            document.Blocks.Add(totalsTable);

            // Footer
            document.Blocks.Add(new Paragraph(new Run(" ")) { FontSize = 20 }); // Spacer
            document.Blocks.Add(new Paragraph(new Run("شكراً لتعاملكم معنا"))
            {
                TextAlignment = TextAlignment.Center,
                FontWeight = FontWeights.Bold,
                FontSize = 14
            });

            return document;
        }

        private static FlowDocument CreateReceiptDocument(Sale sale)
        {
            var document = new FlowDocument
            {
                PageWidth = 300, // Receipt width
                PagePadding = new Thickness(10),
                FontFamily = new FontFamily("Arial"),
                FontSize = 10,
                FlowDirection = FlowDirection.RightToLeft
            };

            // Header
            document.Blocks.Add(new Paragraph(new Run("شركة التمور المتقدمة"))
            {
                TextAlignment = TextAlignment.Center,
                FontWeight = FontWeights.Bold,
                FontSize = 12
            });

            document.Blocks.Add(new Paragraph(new Run($"إيصال رقم: {sale.InvoiceNumber}"))
            {
                TextAlignment = TextAlignment.Center,
                FontWeight = FontWeights.Bold
            });

            document.Blocks.Add(new Paragraph(new Run($"التاريخ: {sale.SaleDate:yyyy/MM/dd HH:mm}"))
            {
                TextAlignment = TextAlignment.Center
            });

            document.Blocks.Add(new Paragraph(new Run("".PadRight(30, '-')))
            {
                TextAlignment = TextAlignment.Center
            });

            // Items
            foreach (var item in sale.SaleItems)
            {
                document.Blocks.Add(new Paragraph(new Run(item.Product?.Name ?? "")));
                document.Blocks.Add(new Paragraph(new Run($"{item.Quantity:N0} × {item.UnitPrice:C} = {item.LineTotal:C}")));
            }

            document.Blocks.Add(new Paragraph(new Run("".PadRight(30, '-')))
            {
                TextAlignment = TextAlignment.Center
            });

            // Total
            document.Blocks.Add(new Paragraph(new Run($"الإجمالي: {sale.TotalAmount:C}"))
            {
                TextAlignment = TextAlignment.Center,
                FontWeight = FontWeights.Bold,
                FontSize = 12
            });

            document.Blocks.Add(new Paragraph(new Run("شكراً لتعاملكم معنا"))
            {
                TextAlignment = TextAlignment.Center,
                FontSize = 8
            });

            return document;
        }

        private static FlowDocument CreateReportDocument(string title, FrameworkElement content)
        {
            var document = new FlowDocument
            {
                PageWidth = 793.7, // A4 width
                PageHeight = 1122.5, // A4 height
                PagePadding = new Thickness(50),
                FontFamily = new FontFamily("Arial"),
                FontSize = 12,
                FlowDirection = FlowDirection.RightToLeft
            };

            // Title
            document.Blocks.Add(new Paragraph(new Run(title))
            {
                TextAlignment = TextAlignment.Center,
                FontWeight = FontWeights.Bold,
                FontSize = 18
            });

            document.Blocks.Add(new Paragraph(new Run($"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}"))
            {
                TextAlignment = TextAlignment.Center,
                FontSize = 10
            });

            document.Blocks.Add(new Paragraph(new Run(" ")) { FontSize = 10 }); // Spacer

            // Content
            var blockUIContainer = new BlockUIContainer(content);
            document.Blocks.Add(blockUIContainer);

            return document;
        }
    }
}
