using System;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using DateFactoryApp.Data;
using DateFactoryApp.Services;
using DateFactoryApp.ViewModels;
using DateFactoryApp.Views;
using DateFactoryApp.Helpers;
using Serilog;
using System.Linq;

namespace DateFactoryApp
{
    public partial class App : Application
    {
        private IServiceProvider? _serviceProvider;

        protected override async void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Configure Serilog
            Log.Logger = new LoggerConfiguration()
                .WriteTo.File("logs/app-.txt", rollingInterval: RollingInterval.Day)
                .CreateLogger();

            try
            {
                // Configure dependency injection
                var services = new ServiceCollection();
                ConfigureServices(services);
                _serviceProvider = services.BuildServiceProvider();

                // Initialize database
                await InitializeDatabaseAsync();

                // Log system start
                AuditLogger.LogSystemStart();

                // Show splash window first
                var splashWindow = new SplashWindow();
                splashWindow.Show();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Application failed to start");
                MessageBox.Show($"فشل في بدء التطبيق: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        private void ConfigureServices(IServiceCollection services)
        {
            // Database
            services.AddDbContext<AppDbContext>(options =>
                options.UseSqlite("Data Source=DateFactory.db"));

            // Services
            services.AddSingleton<IUserService, UserService>();
            services.AddSingleton<IProductService, ProductService>();
            services.AddSingleton<ISalesService, SalesService>();
            services.AddSingleton<IInventoryService, InventoryService>();
            services.AddSingleton<ICustomerService, CustomerService>();
            services.AddSingleton<ISupplierService, SupplierService>();
            services.AddSingleton<IReportService, ReportService>();
            services.AddSingleton<IBackupService, BackupService>();
            services.AddSingleton<ISyncService, SyncService>();
            services.AddSingleton<IThemeService, ThemeService>();

            // ViewModels
            services.AddTransient<LoginViewModel>();
            services.AddTransient<MainViewModel>();
            services.AddTransient<DashboardViewModel>();
            services.AddTransient<ProductsViewModel>();
            services.AddTransient<SalesViewModel>();
            services.AddTransient<InventoryViewModel>();
            services.AddTransient<CustomersViewModel>();
            services.AddTransient<SuppliersViewModel>();
            services.AddTransient<ReportsViewModel>();
            services.AddTransient<SettingsViewModel>();
            services.AddTransient<ProductionViewModel>();
        }

        public static T GetService<T>() where T : class
        {
            var app = Current as App;
            if (app?._serviceProvider == null)
                throw new InvalidOperationException("Service provider not initialized");

            return app._serviceProvider.GetRequiredService<T>();
        }

        public static object GetService(Type serviceType)
        {
            var app = Current as App;
            if (app?._serviceProvider == null)
                throw new InvalidOperationException("Service provider not initialized");

            return app._serviceProvider.GetRequiredService(serviceType);
        }

        private async Task InitializeDatabaseAsync()
        {
            try
            {
                using var scope = _serviceProvider!.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

                // Ensure database is created
                await context.Database.EnsureCreatedAsync();

                // Create default admin user if not exists
                await CreateDefaultUserAsync(context);

                Log.Information("Database initialized successfully");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to initialize database");
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private async Task CreateDefaultUserAsync(AppDbContext context)
        {
            try
            {
                var adminExists = await context.Users.AnyAsync(u => u.Username == "admin");
                if (!adminExists)
                {
                    var adminUser = new Models.User
                    {
                        Username = "admin",
                        PasswordHash = "admin", // In real app, this should be hashed
                        FullName = "مدير النظام",
                        Role = Models.UserRole.Manager,
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    };

                    context.Users.Add(adminUser);
                    await context.SaveChangesAsync();
                    Log.Information("Default admin user created");
                }
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Failed to create default user");
                // Don't throw here, as this is not critical
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            // Log system shutdown
            AuditLogger.LogSystemShutdown();

            _serviceProvider?.GetService<IServiceScope>()?.Dispose();
            Log.CloseAndFlush();
            base.OnExit(e);
        }
    }
}
