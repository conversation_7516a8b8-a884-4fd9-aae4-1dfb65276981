<Window x:Class="DateFactoryApp.Views.SplashWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="نظام إدارة مصنع ومحل التمور"
        Height="500" Width="800"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Animations -->
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:1"/>
        </Storyboard>
        
        <Storyboard x:Key="SlideInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)" 
                           From="50" To="0" Duration="0:0:0.8">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <Storyboard x:Key="PulseAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           From="1" To="1.1" Duration="0:0:1" AutoReverse="True">
                <DoubleAnimation.EasingFunction>
                    <SineEase EasingMode="EaseInOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           From="1" To="1.1" Duration="0:0:1" AutoReverse="True">
                <DoubleAnimation.EasingFunction>
                    <SineEase EasingMode="EaseInOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
    </Window.Resources>

    <Window.Triggers>
        <EventTrigger RoutedEvent="Window.Loaded">
            <BeginStoryboard Storyboard="{StaticResource FadeInAnimation}"/>
        </EventTrigger>
    </Window.Triggers>

    <materialDesign:Card materialDesign:ShadowAssist.ShadowDepth="Depth5" 
                         materialDesign:ShadowAssist.ShadowEdges="All"
                         CornerRadius="20">
        <Grid>
            <!-- Background Gradient -->
            <Grid.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#8D6E63" Offset="0"/>
                    <GradientStop Color="#A1887F" Offset="0.5"/>
                    <GradientStop Color="#BCAAA4" Offset="1"/>
                </LinearGradientBrush>
            </Grid.Background>

            <!-- Decorative Pattern -->
            <Canvas Opacity="0.1">
                <Ellipse Width="200" Height="200" Fill="White" Canvas.Left="-50" Canvas.Top="-50"/>
                <Ellipse Width="150" Height="150" Fill="White" Canvas.Right="-30" Canvas.Bottom="-30"/>
                <Ellipse Width="100" Height="100" Fill="White" Canvas.Left="100" Canvas.Bottom="-20"/>
            </Canvas>

            <!-- Main Content -->
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                
                <!-- Logo Section -->
                <StackPanel HorizontalAlignment="Center" Margin="0,0,0,40">
                    <materialDesign:PackIcon Kind="Factory" 
                                           Width="120" Height="120" 
                                           Foreground="White"
                                           HorizontalAlignment="Center"
                                           RenderTransformOrigin="0.5,0.5">
                        <materialDesign:PackIcon.RenderTransform>
                            <ScaleTransform/>
                        </materialDesign:PackIcon.RenderTransform>
                        <materialDesign:PackIcon.Triggers>
                            <EventTrigger RoutedEvent="FrameworkElement.Loaded">
                                <BeginStoryboard Storyboard="{StaticResource PulseAnimation}"/>
                            </EventTrigger>
                        </materialDesign:PackIcon.Triggers>
                    </materialDesign:PackIcon>
                </StackPanel>

                <!-- Title Section -->
                <StackPanel HorizontalAlignment="Center" Margin="0,0,0,30"
                          RenderTransformOrigin="0.5,0.5">
                    <StackPanel.RenderTransform>
                        <TranslateTransform/>
                    </StackPanel.RenderTransform>
                    <StackPanel.Triggers>
                        <EventTrigger RoutedEvent="FrameworkElement.Loaded">
                            <BeginStoryboard Storyboard="{StaticResource SlideInAnimation}"/>
                        </EventTrigger>
                    </StackPanel.Triggers>
                    
                    <TextBlock Text="نظام إدارة مصنع ومحل التمور"
                             Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                             Foreground="White"
                             HorizontalAlignment="Center"
                             FontWeight="Bold"
                             Margin="0,0,0,8"/>
                    
                    <TextBlock Text="Date Factory Management System"
                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                             Foreground="White"
                             HorizontalAlignment="Center"
                             Opacity="0.9"
                             FontStyle="Italic"/>
                </StackPanel>

                <!-- Version and Company Info -->
                <StackPanel HorizontalAlignment="Center" Margin="0,0,0,40">
                    <TextBlock Text="الإصدار 1.0.0"
                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                             Foreground="White"
                             HorizontalAlignment="Center"
                             Opacity="0.8"
                             Margin="0,0,0,4"/>
                    
                    <TextBlock Text="© 2024 شركة التمور المتقدمة"
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             Foreground="White"
                             HorizontalAlignment="Center"
                             Opacity="0.7"/>
                </StackPanel>

                <!-- Loading Section -->
                <StackPanel HorizontalAlignment="Center">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                               Width="40" Height="40"
                               IsIndeterminate="True"
                               Foreground="White"
                               Margin="0,0,0,16"/>
                    
                    <TextBlock x:Name="LoadingText"
                             Text="جاري التحميل..."
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             Foreground="White"
                             HorizontalAlignment="Center"
                             Opacity="0.9"/>
                </StackPanel>

                <!-- Features Preview -->
                <StackPanel HorizontalAlignment="Center" Margin="0,40,0,0" Opacity="0.8">
                    <TextBlock Text="الميزات الرئيسية:"
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             Foreground="White"
                             HorizontalAlignment="Center"
                             FontWeight="Medium"
                             Margin="0,0,0,12"/>
                    
                    <UniformGrid Columns="2" HorizontalAlignment="Center">
                        <StackPanel Orientation="Horizontal" Margin="8,4">
                            <materialDesign:PackIcon Kind="CashRegister" Width="16" Height="16" 
                                                   Foreground="White" VerticalAlignment="Center" Margin="0,0,4,0"/>
                            <TextBlock Text="إدارة المبيعات" Foreground="White" 
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="8,4">
                            <materialDesign:PackIcon Kind="Package" Width="16" Height="16" 
                                                   Foreground="White" VerticalAlignment="Center" Margin="0,0,4,0"/>
                            <TextBlock Text="إدارة المخزون" Foreground="White" 
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="8,4">
                            <materialDesign:PackIcon Kind="AccountGroup" Width="16" Height="16" 
                                                   Foreground="White" VerticalAlignment="Center" Margin="0,0,4,0"/>
                            <TextBlock Text="إدارة العملاء" Foreground="White" 
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="8,4">
                            <materialDesign:PackIcon Kind="ChartLine" Width="16" Height="16" 
                                                   Foreground="White" VerticalAlignment="Center" Margin="0,0,4,0"/>
                            <TextBlock Text="التقارير المتقدمة" Foreground="White" 
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                        </StackPanel>
                    </UniformGrid>
                </StackPanel>
            </StackPanel>
        </Grid>
    </materialDesign:Card>
</Window>
