<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Card Styles -->
    <Style x:Key="DashboardCard" TargetType="materialDesign:Card">
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowEdges" Value="All"/>
        <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
    </Style>



    <!-- Button Styles -->
    <Style x:Key="PrimaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="BorderBrush" Value="{StaticResource SecondaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource SecondaryBrush}"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Padding" Value="16,8"/>
    </Style>

    <Style x:Key="SuccessButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Padding" Value="16,8"/>
    </Style>

    <Style x:Key="WarningButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource WarningBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Padding" Value="16,8"/>
    </Style>

    <Style x:Key="ErrorButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Padding" Value="16,8"/>
    </Style>

    <!-- Text Styles -->
    <Style x:Key="PageTitle" TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignHeadline4TextBlock}">
        <Setter Property="Margin" Value="0,0,0,16"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
    </Style>

    <Style x:Key="SectionTitle" TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignHeadline6TextBlock}">
        <Setter Property="Margin" Value="0,16,0,8"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
    </Style>

    <Style x:Key="StatNumber" TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignHeadline3TextBlock}">
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
    </Style>

    <Style x:Key="StatLabel" TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignBody2TextBlock}">
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
        <Setter Property="Margin" Value="0,4,0,0"/>
    </Style>

    <!-- Input Styles -->
    <Style x:Key="FormTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        <Setter Property="materialDesign:TextFieldAssist.HasClearButton" Value="True"/>
    </Style>

    <Style x:Key="FormComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
    </Style>

    <Style x:Key="FormDatePicker" TargetType="DatePicker" BasedOn="{StaticResource MaterialDesignOutlinedDatePicker}">
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
    </Style>

    <!-- DataGrid Styles -->
    <Style x:Key="CustomDataGrid" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="materialDesign:DataGridAssist.CellPadding" Value="8"/>
        <Setter Property="materialDesign:DataGridAssist.ColumnHeaderPadding" Value="8"/>
    </Style>

    <!-- Navigation Styles -->
    <Style x:Key="NavigationListBox" TargetType="ListBox" BasedOn="{StaticResource MaterialDesignNavigationPrimaryListBox}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
    </Style>

    <Style x:Key="NavigationListBoxItem" TargetType="ListBoxItem" BasedOn="{StaticResource MaterialDesignNavigationPrimaryListBoxItem}">
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="Margin" Value="4,2"/>
    </Style>

    <!-- Icon Styles -->
    <Style x:Key="MenuIcon" TargetType="materialDesign:PackIcon">
        <Setter Property="Width" Value="24"/>
        <Setter Property="Height" Value="24"/>
        <Setter Property="Margin" Value="0,0,16,0"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <Style x:Key="StatIcon" TargetType="materialDesign:PackIcon">
        <Setter Property="Width" Value="32"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Foreground" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>

    <!-- Panel Styles -->
    <Style x:Key="FormPanel" TargetType="StackPanel">
        <Setter Property="Margin" Value="16"/>
        <Setter Property="Orientation" Value="Vertical"/>
    </Style>

    <Style x:Key="ButtonPanel" TargetType="StackPanel">
        <Setter Property="Orientation" Value="Horizontal"/>
        <Setter Property="HorizontalAlignment" Value="Right"/>
        <Setter Property="Margin" Value="0,16,0,0"/>
    </Style>

    <!-- Progress Bar Styles -->
    <Style x:Key="CustomProgressBar" TargetType="ProgressBar" BasedOn="{StaticResource MaterialDesignLinearProgressBar}">
        <Setter Property="Height" Value="6"/>
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
    </Style>

    <!-- Snackbar Styles -->
    <Style x:Key="SuccessSnackbar" TargetType="materialDesign:Snackbar">
        <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <Style x:Key="ErrorSnackbar" TargetType="materialDesign:Snackbar">
        <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <Style x:Key="WarningSnackbar" TargetType="materialDesign:Snackbar">
        <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>
    <!-- Additional Text Styles -->
    <Style x:Key="CardTitle" TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignSubtitle1TextBlock}">
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Margin" Value="0,0,0,4"/>
    </Style>

    <Style x:Key="CardValue" TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignHeadline5TextBlock}">
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
    </Style>

    <Style x:Key="BodyText" TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignBody1TextBlock}">
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="LineHeight" Value="20"/>
    </Style>





</ResourceDictionary>
