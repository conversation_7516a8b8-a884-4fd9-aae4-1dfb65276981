<Window x:Class="DateFactoryApp.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام إدارة مصنع التمور"
        Height="600" Width="450"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{materialDesign:MaterialDesignFont}"
        FlowDirection="RightToLeft"
        WindowStyle="None"
        AllowsTransparency="True">

    <Window.Resources>
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.5"/>
        </Storyboard>
    </Window.Resources>

    <Window.Triggers>
        <EventTrigger RoutedEvent="Window.Loaded">
            <BeginStoryboard Storyboard="{StaticResource FadeInAnimation}"/>
        </EventTrigger>
    </Window.Triggers>

    <materialDesign:Card materialDesign:ShadowAssist.ShadowDepth="Depth4"
                         materialDesign:ShadowAssist.ShadowEdges="All"
                         CornerRadius="15">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="200"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header Section -->
            <Grid Grid.Row="0" Background="{StaticResource PrimaryBrush}">
                <Grid.Effect>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                </Grid.Effect>

                <!-- Close Button -->
                <Button Style="{StaticResource MaterialDesignIconButton}"
                        HorizontalAlignment="Left" VerticalAlignment="Top"
                        Margin="10" Foreground="White"
                        Click="CloseButton_Click">
                    <materialDesign:PackIcon Kind="Close" Width="20" Height="20"/>
                </Button>

                <!-- Logo and Title -->
                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Factory" Width="64" Height="64"
                                           Foreground="White" Margin="0,0,0,16"/>
                    <TextBlock Text="نظام إدارة مصنع ومحل التمور"
                             Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                             Foreground="White" HorizontalAlignment="Center"
                             FontWeight="Bold"/>
                    <TextBlock Text="الإصدار 1.0"
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             Foreground="White" HorizontalAlignment="Center"
                             Opacity="0.8" Margin="0,4,0,0"/>
                </StackPanel>
            </Grid>

            <!-- Login Form -->
            <StackPanel Grid.Row="1" Margin="40,30,40,40" VerticalAlignment="Center">

                <!-- Welcome Text -->
                <TextBlock Text="مرحباً بك"
                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                         HorizontalAlignment="Center" Margin="0,0,0,8"
                         Foreground="{StaticResource PrimaryBrush}"/>

                <TextBlock Text="يرجى تسجيل الدخول للمتابعة"
                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                         HorizontalAlignment="Center" Margin="0,0,0,32"
                         Opacity="0.7"/>

                <!-- Username Field -->
                <TextBox x:Name="UsernameTextBox"
                         Text="admin"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         materialDesign:HintAssist.Hint="اسم المستخدم"
                         materialDesign:HintAssist.IsFloating="True"
                         materialDesign:TextFieldAssist.HasClearButton="True"
                         Margin="0,0,0,16"/>

                <!-- Password Field -->
                <PasswordBox x:Name="PasswordBox"
                           Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                           materialDesign:HintAssist.Hint="كلمة المرور"
                           materialDesign:HintAssist.IsFloating="True"
                           materialDesign:TextFieldAssist.HasClearButton="True"
                           Margin="0,0,0,16"/>

                <!-- Remember Me -->
                <CheckBox x:Name="RememberMeCheckBox"
                        Content="تذكرني"
                        Style="{StaticResource MaterialDesignCheckBox}"
                        Margin="0,0,0,24"
                        Foreground="{DynamicResource MaterialDesignBody}"/>

                <!-- Login Button -->
                <Button x:Name="LoginButton"
                      Content="تسجيل الدخول"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Background="{StaticResource PrimaryBrush}"
                      Foreground="White"
                      Height="48"
                      FontSize="16"
                      FontWeight="Medium"
                      Click="LoginButton_Click"
                      Margin="0,0,0,16">
                    <Button.Effect>
                        <DropShadowEffect Color="{Binding Source={StaticResource PrimaryBrush}, Path=Color}"
                                        Direction="270" ShadowDepth="2" Opacity="0.3"/>
                    </Button.Effect>
                </Button>

                <!-- Error Message -->
                <materialDesign:Card x:Name="ErrorCard"
                                   Background="{StaticResource ErrorBrush}"
                                   Visibility="Collapsed"
                                   Margin="0,8,0,0">
                    <StackPanel Orientation="Horizontal" Margin="16,12">
                        <materialDesign:PackIcon Kind="AlertCircle"
                                               Width="20" Height="20"
                                               Foreground="White"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock x:Name="ErrorMessageTextBlock"
                                 Foreground="White"
                                 VerticalAlignment="Center"
                                 FontWeight="Medium"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Loading Indicator -->
                <StackPanel x:Name="LoadingPanel"
                          Orientation="Horizontal"
                          HorizontalAlignment="Center"
                          Visibility="Collapsed"
                          Margin="0,16,0,0">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                               Width="20" Height="20"
                               IsIndeterminate="True"
                               Margin="0,0,8,0"/>
                    <TextBlock Text="جاري تسجيل الدخول..."
                             VerticalAlignment="Center"
                             Opacity="0.7"/>
                </StackPanel>

                <!-- Footer -->
                <StackPanel HorizontalAlignment="Center" Margin="0,24,0,0">
                    <TextBlock Text="© 2024 شركة التمور المتقدمة"
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"
                             Opacity="0.6"/>
                    <TextBlock Text="جميع الحقوق محفوظة"
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             HorizontalAlignment="Center"
                             Opacity="0.6"/>
                </StackPanel>
            </StackPanel>
        </Grid>
    </materialDesign:Card>
</Window>
